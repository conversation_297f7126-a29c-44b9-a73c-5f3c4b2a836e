html {
  -webkit-text-size-adjust: 100%;
  -ms-text-size-adjust: 100%;
  font-family: sans-serif;
}

body {
  margin: 0;
}

article, aside, details, figcaption, figure, footer, header, hgroup, main, menu, nav, section, summary {
  display: block;
}

audio, canvas, progress, video {
  vertical-align: baseline;
  display: inline-block;
}

audio:not([controls]) {
  height: 0;
  display: none;
}

[hidden], template {
  display: none;
}

a {
  background-color: #0000;
}

a:active, a:hover {
  outline: 0;
}

abbr[title] {
  border-bottom: 1px dotted;
}

b, strong {
  font-weight: bold;
}

dfn {
  font-style: italic;
}

h1 {
  margin: .67em 0;
  font-size: 2em;
}

mark {
  color: #000;
  background: #ff0;
}

small {
  font-size: 80%;
}

sub, sup {
  vertical-align: baseline;
  font-size: 75%;
  line-height: 0;
  position: relative;
}

sup {
  top: -.5em;
}

sub {
  bottom: -.25em;
}

img {
  border: 0;
}

svg:not(:root) {
  overflow: hidden;
}

hr {
  box-sizing: content-box;
  height: 0;
}

pre {
  overflow: auto;
}

code, kbd, pre, samp {
  font-family: monospace;
  font-size: 1em;
}

button, input, optgroup, select, textarea {
  color: inherit;
  font: inherit;
  margin: 0;
}

button {
  overflow: visible;
}

button, select {
  text-transform: none;
}

button, html input[type="button"], input[type="reset"] {
  -webkit-appearance: button;
  cursor: pointer;
}

button[disabled], html input[disabled] {
  cursor: default;
}

button::-moz-focus-inner, input::-moz-focus-inner {
  border: 0;
  padding: 0;
}

input {
  line-height: normal;
}

input[type="checkbox"], input[type="radio"] {
  box-sizing: border-box;
  padding: 0;
}

input[type="number"]::-webkit-inner-spin-button, input[type="number"]::-webkit-outer-spin-button {
  height: auto;
}

input[type="search"] {
  -webkit-appearance: none;
}

input[type="search"]::-webkit-search-cancel-button, input[type="search"]::-webkit-search-decoration {
  -webkit-appearance: none;
}

legend {
  border: 0;
  padding: 0;
}

textarea {
  overflow: auto;
}

optgroup {
  font-weight: bold;
}

table {
  border-collapse: collapse;
  border-spacing: 0;
}

td, th {
  padding: 0;
}

@font-face {
  font-family: webflow-icons;
  src: url("data:application/x-font-ttf;charset=utf-8;base64,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") format("truetype");
  font-weight: normal;
  font-style: normal;
}

[class^="w-icon-"], [class*=" w-icon-"] {
  speak: none;
  font-variant: normal;
  text-transform: none;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-style: normal;
  font-weight: normal;
  line-height: 1;
  font-family: webflow-icons !important;
}

.w-icon-slider-right:before {
  content: "î˜€";
}

.w-icon-slider-left:before {
  content: "î˜";
}

.w-icon-nav-menu:before {
  content: "î˜‚";
}

.w-icon-arrow-down:before, .w-icon-dropdown-toggle:before {
  content: "î˜ƒ";
}

.w-icon-file-upload-remove:before {
  content: "î¤€";
}

.w-icon-file-upload-icon:before {
  content: "î¤ƒ";
}

* {
  box-sizing: border-box;
}

html {
  height: 100%;
}

body {
  color: #333;
  background-color: #fff;
  min-height: 100%;
  margin: 0;
  font-family: Arial, sans-serif;
  font-size: 14px;
  line-height: 20px;
}

img {
  vertical-align: middle;
  max-width: 100%;
  display: inline-block;
}

html.w-mod-touch * {
  background-attachment: scroll !important;
}

.w-block {
  display: block;
}

.w-inline-block {
  max-width: 100%;
  display: inline-block;
}

.w-clearfix:before, .w-clearfix:after {
  content: " ";
  grid-area: 1 / 1 / 2 / 2;
  display: table;
}

.w-clearfix:after {
  clear: both;
}

.w-hidden {
  display: none;
}

.w-button {
  color: #fff;
  line-height: inherit;
  cursor: pointer;
  background-color: #3898ec;
  border: 0;
  border-radius: 0;
  padding: 9px 15px;
  text-decoration: none;
  display: inline-block;
}

input.w-button {
  -webkit-appearance: button;
}

html[data-w-dynpage] [data-w-cloak] {
  color: #0000 !important;
}

.w-code-block {
  margin: unset;
}

pre.w-code-block code {
  all: inherit;
}

.w-optimization {
  display: contents;
}

.w-webflow-badge, .w-webflow-badge > img {
  box-sizing: unset;
  width: unset;
  height: unset;
  max-height: unset;
  max-width: unset;
  min-height: unset;
  min-width: unset;
  margin: unset;
  padding: unset;
  float: unset;
  clear: unset;
  border: unset;
  border-radius: unset;
  background: unset;
  background-image: unset;
  background-position: unset;
  background-size: unset;
  background-repeat: unset;
  background-origin: unset;
  background-clip: unset;
  background-attachment: unset;
  background-color: unset;
  box-shadow: unset;
  transform: unset;
  direction: unset;
  font-family: unset;
  font-weight: unset;
  color: unset;
  font-size: unset;
  line-height: unset;
  font-style: unset;
  font-variant: unset;
  text-align: unset;
  letter-spacing: unset;
  -webkit-text-decoration: unset;
  text-decoration: unset;
  text-indent: unset;
  text-transform: unset;
  list-style-type: unset;
  text-shadow: unset;
  vertical-align: unset;
  cursor: unset;
  white-space: unset;
  word-break: unset;
  word-spacing: unset;
  word-wrap: unset;
  transition: unset;
}

.w-webflow-badge {
  white-space: nowrap;
  cursor: pointer;
  box-shadow: 0 0 0 1px #0000001a, 0 1px 3px #0000001a;
  visibility: visible !important;
  opacity: 1 !important;
  z-index: 2147483647 !important;
  color: #aaadb0 !important;
  overflow: unset !important;
  background-color: #fff !important;
  border-radius: 3px !important;
  width: auto !important;
  height: auto !important;
  margin: 0 !important;
  padding: 6px !important;
  font-size: 12px !important;
  line-height: 14px !important;
  text-decoration: none !important;
  display: inline-block !important;
  position: fixed !important;
  inset: auto 12px 12px auto !important;
  transform: none !important;
}

.w-webflow-badge > img {
  position: unset;
  visibility: unset !important;
  opacity: 1 !important;
  vertical-align: middle !important;
  display: inline-block !important;
}

h1, h2, h3, h4, h5, h6 {
  margin-bottom: 10px;
  font-weight: bold;
}

h1 {
  margin-top: 20px;
  font-size: 38px;
  line-height: 44px;
}

h2 {
  margin-top: 20px;
  font-size: 32px;
  line-height: 36px;
}

h3 {
  margin-top: 20px;
  font-size: 24px;
  line-height: 30px;
}

h4 {
  margin-top: 10px;
  font-size: 18px;
  line-height: 24px;
}

h5 {
  margin-top: 10px;
  font-size: 14px;
  line-height: 20px;
}

h6 {
  margin-top: 10px;
  font-size: 12px;
  line-height: 18px;
}

p {
  margin-top: 0;
  margin-bottom: 10px;
}

blockquote {
  border-left: 5px solid #e2e2e2;
  margin: 0 0 10px;
  padding: 10px 20px;
  font-size: 18px;
  line-height: 22px;
}

figure {
  margin: 0 0 10px;
}

figcaption {
  text-align: center;
  margin-top: 5px;
}

ul, ol {
  margin-top: 0;
  margin-bottom: 10px;
  padding-left: 40px;
}

.w-list-unstyled {
  padding-left: 0;
  list-style: none;
}

.w-embed:before, .w-embed:after {
  content: " ";
  grid-area: 1 / 1 / 2 / 2;
  display: table;
}

.w-embed:after {
  clear: both;
}

.w-video {
  width: 100%;
  padding: 0;
  position: relative;
}

.w-video iframe, .w-video object, .w-video embed {
  border: none;
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
}

fieldset {
  border: 0;
  margin: 0;
  padding: 0;
}

button, [type="button"], [type="reset"] {
  cursor: pointer;
  -webkit-appearance: button;
  border: 0;
}

.w-form {
  margin: 0 0 15px;
}

.w-form-done {
  text-align: center;
  background-color: #ddd;
  padding: 20px;
  display: none;
}

.w-form-fail {
  background-color: #ffdede;
  margin-top: 10px;
  padding: 10px;
  display: none;
}

label {
  margin-bottom: 5px;
  font-weight: bold;
  display: block;
}

.w-input, .w-select {
  color: #333;
  vertical-align: middle;
  background-color: #fff;
  border: 1px solid #ccc;
  width: 100%;
  height: 38px;
  margin-bottom: 10px;
  padding: 8px 12px;
  font-size: 14px;
  line-height: 1.42857;
  display: block;
}

.w-input::placeholder, .w-select::placeholder {
  color: #999;
}

.w-input:focus, .w-select:focus {
  border-color: #3898ec;
  outline: 0;
}

.w-input[disabled], .w-select[disabled], .w-input[readonly], .w-select[readonly], fieldset[disabled] .w-input, fieldset[disabled] .w-select {
  cursor: not-allowed;
}

.w-input[disabled]:not(.w-input-disabled), .w-select[disabled]:not(.w-input-disabled), .w-input[readonly], .w-select[readonly], fieldset[disabled]:not(.w-input-disabled) .w-input, fieldset[disabled]:not(.w-input-disabled) .w-select {
  background-color: #eee;
}

textarea.w-input, textarea.w-select {
  height: auto;
}

.w-select {
  background-color: #f3f3f3;
}

.w-select[multiple] {
  height: auto;
}

.w-form-label {
  cursor: pointer;
  margin-bottom: 0;
  font-weight: normal;
  display: inline-block;
}

.w-radio {
  margin-bottom: 5px;
  padding-left: 20px;
  display: block;
}

.w-radio:before, .w-radio:after {
  content: " ";
  grid-area: 1 / 1 / 2 / 2;
  display: table;
}

.w-radio:after {
  clear: both;
}

.w-radio-input {
  float: left;
  margin: 3px 0 0 -20px;
  line-height: normal;
}

.w-file-upload {
  margin-bottom: 10px;
  display: block;
}

.w-file-upload-input {
  opacity: 0;
  z-index: -100;
  width: .1px;
  height: .1px;
  position: absolute;
  overflow: hidden;
}

.w-file-upload-default, .w-file-upload-uploading, .w-file-upload-success {
  color: #333;
  display: inline-block;
}

.w-file-upload-error {
  margin-top: 10px;
  display: block;
}

.w-file-upload-default.w-hidden, .w-file-upload-uploading.w-hidden, .w-file-upload-error.w-hidden, .w-file-upload-success.w-hidden {
  display: none;
}

.w-file-upload-uploading-btn {
  cursor: pointer;
  background-color: #fafafa;
  border: 1px solid #ccc;
  margin: 0;
  padding: 8px 12px;
  font-size: 14px;
  font-weight: normal;
  display: flex;
}

.w-file-upload-file {
  background-color: #fafafa;
  border: 1px solid #ccc;
  flex-grow: 1;
  justify-content: space-between;
  margin: 0;
  padding: 8px 9px 8px 11px;
  display: flex;
}

.w-file-upload-file-name {
  font-size: 14px;
  font-weight: normal;
  display: block;
}

.w-file-remove-link {
  cursor: pointer;
  width: auto;
  height: auto;
  margin-top: 3px;
  margin-left: 10px;
  padding: 3px;
  display: block;
}

.w-icon-file-upload-remove {
  margin: auto;
  font-size: 10px;
}

.w-file-upload-error-msg {
  color: #ea384c;
  padding: 2px 0;
  display: inline-block;
}

.w-file-upload-info {
  padding: 0 12px;
  line-height: 38px;
  display: inline-block;
}

.w-file-upload-label {
  cursor: pointer;
  background-color: #fafafa;
  border: 1px solid #ccc;
  margin: 0;
  padding: 8px 12px;
  font-size: 14px;
  font-weight: normal;
  display: inline-block;
}

.w-icon-file-upload-icon, .w-icon-file-upload-uploading {
  width: 20px;
  margin-right: 8px;
  display: inline-block;
}

.w-icon-file-upload-uploading {
  height: 20px;
}

.w-container {
  max-width: 940px;
  margin-left: auto;
  margin-right: auto;
}

.w-container:before, .w-container:after {
  content: " ";
  grid-area: 1 / 1 / 2 / 2;
  display: table;
}

.w-container:after {
  clear: both;
}

.w-container .w-row {
  margin-left: -10px;
  margin-right: -10px;
}

.w-row:before, .w-row:after {
  content: " ";
  grid-area: 1 / 1 / 2 / 2;
  display: table;
}

.w-row:after {
  clear: both;
}

.w-row .w-row {
  margin-left: 0;
  margin-right: 0;
}

.w-col {
  float: left;
  width: 100%;
  min-height: 1px;
  padding-left: 10px;
  padding-right: 10px;
  position: relative;
}

.w-col .w-col {
  padding-left: 0;
  padding-right: 0;
}

.w-col-1 {
  width: 8.33333%;
}

.w-col-2 {
  width: 16.6667%;
}

.w-col-3 {
  width: 25%;
}

.w-col-4 {
  width: 33.3333%;
}

.w-col-5 {
  width: 41.6667%;
}

.w-col-6 {
  width: 50%;
}

.w-col-7 {
  width: 58.3333%;
}

.w-col-8 {
  width: 66.6667%;
}

.w-col-9 {
  width: 75%;
}

.w-col-10 {
  width: 83.3333%;
}

.w-col-11 {
  width: 91.6667%;
}

.w-col-12 {
  width: 100%;
}

.w-hidden-main {
  display: none !important;
}

@media screen and (max-width: 991px) {
  .w-container {
    max-width: 728px;
  }

  .w-hidden-main {
    display: inherit !important;
  }

  .w-hidden-medium {
    display: none !important;
  }

  .w-col-medium-1 {
    width: 8.33333%;
  }

  .w-col-medium-2 {
    width: 16.6667%;
  }

  .w-col-medium-3 {
    width: 25%;
  }

  .w-col-medium-4 {
    width: 33.3333%;
  }

  .w-col-medium-5 {
    width: 41.6667%;
  }

  .w-col-medium-6 {
    width: 50%;
  }

  .w-col-medium-7 {
    width: 58.3333%;
  }

  .w-col-medium-8 {
    width: 66.6667%;
  }

  .w-col-medium-9 {
    width: 75%;
  }

  .w-col-medium-10 {
    width: 83.3333%;
  }

  .w-col-medium-11 {
    width: 91.6667%;
  }

  .w-col-medium-12 {
    width: 100%;
  }

  .w-col-stack {
    width: 100%;
    left: auto;
    right: auto;
  }
}

@media screen and (max-width: 767px) {
  .w-hidden-main, .w-hidden-medium {
    display: inherit !important;
  }

  .w-hidden-small {
    display: none !important;
  }

  .w-row, .w-container .w-row {
    margin-left: 0;
    margin-right: 0;
  }

  .w-col {
    width: 100%;
    left: auto;
    right: auto;
  }

  .w-col-small-1 {
    width: 8.33333%;
  }

  .w-col-small-2 {
    width: 16.6667%;
  }

  .w-col-small-3 {
    width: 25%;
  }

  .w-col-small-4 {
    width: 33.3333%;
  }

  .w-col-small-5 {
    width: 41.6667%;
  }

  .w-col-small-6 {
    width: 50%;
  }

  .w-col-small-7 {
    width: 58.3333%;
  }

  .w-col-small-8 {
    width: 66.6667%;
  }

  .w-col-small-9 {
    width: 75%;
  }

  .w-col-small-10 {
    width: 83.3333%;
  }

  .w-col-small-11 {
    width: 91.6667%;
  }

  .w-col-small-12 {
    width: 100%;
  }
}

@media screen and (max-width: 479px) {
  .w-container {
    max-width: none;
  }

  .w-hidden-main, .w-hidden-medium, .w-hidden-small {
    display: inherit !important;
  }

  .w-hidden-tiny {
    display: none !important;
  }

  .w-col {
    width: 100%;
  }

  .w-col-tiny-1 {
    width: 8.33333%;
  }

  .w-col-tiny-2 {
    width: 16.6667%;
  }

  .w-col-tiny-3 {
    width: 25%;
  }

  .w-col-tiny-4 {
    width: 33.3333%;
  }

  .w-col-tiny-5 {
    width: 41.6667%;
  }

  .w-col-tiny-6 {
    width: 50%;
  }

  .w-col-tiny-7 {
    width: 58.3333%;
  }

  .w-col-tiny-8 {
    width: 66.6667%;
  }

  .w-col-tiny-9 {
    width: 75%;
  }

  .w-col-tiny-10 {
    width: 83.3333%;
  }

  .w-col-tiny-11 {
    width: 91.6667%;
  }

  .w-col-tiny-12 {
    width: 100%;
  }
}

.w-widget {
  position: relative;
}

.w-widget-map {
  width: 100%;
  height: 400px;
}

.w-widget-map label {
  width: auto;
  display: inline;
}

.w-widget-map img {
  max-width: inherit;
}

.w-widget-map .gm-style-iw {
  text-align: center;
}

.w-widget-map .gm-style-iw > button {
  display: none !important;
}

.w-widget-twitter {
  overflow: hidden;
}

.w-widget-twitter-count-shim {
  vertical-align: top;
  text-align: center;
  background: #fff;
  border: 1px solid #758696;
  border-radius: 3px;
  width: 28px;
  height: 20px;
  display: inline-block;
  position: relative;
}

.w-widget-twitter-count-shim * {
  pointer-events: none;
  -webkit-user-select: none;
  user-select: none;
}

.w-widget-twitter-count-shim .w-widget-twitter-count-inner {
  text-align: center;
  color: #999;
  font-family: serif;
  font-size: 15px;
  line-height: 12px;
  position: relative;
}

.w-widget-twitter-count-shim .w-widget-twitter-count-clear {
  display: block;
  position: relative;
}

.w-widget-twitter-count-shim.w--large {
  width: 36px;
  height: 28px;
}

.w-widget-twitter-count-shim.w--large .w-widget-twitter-count-inner {
  font-size: 18px;
  line-height: 18px;
}

.w-widget-twitter-count-shim:not(.w--vertical) {
  margin-left: 5px;
  margin-right: 8px;
}

.w-widget-twitter-count-shim:not(.w--vertical).w--large {
  margin-left: 6px;
}

.w-widget-twitter-count-shim:not(.w--vertical):before, .w-widget-twitter-count-shim:not(.w--vertical):after {
  content: " ";
  pointer-events: none;
  border: solid #0000;
  width: 0;
  height: 0;
  position: absolute;
  top: 50%;
  left: 0;
}

.w-widget-twitter-count-shim:not(.w--vertical):before {
  border-width: 4px;
  border-color: #75869600 #5d6c7b #75869600 #75869600;
  margin-top: -4px;
  margin-left: -9px;
}

.w-widget-twitter-count-shim:not(.w--vertical).w--large:before {
  border-width: 5px;
  margin-top: -5px;
  margin-left: -10px;
}

.w-widget-twitter-count-shim:not(.w--vertical):after {
  border-width: 4px;
  border-color: #fff0 #fff #fff0 #fff0;
  margin-top: -4px;
  margin-left: -8px;
}

.w-widget-twitter-count-shim:not(.w--vertical).w--large:after {
  border-width: 5px;
  margin-top: -5px;
  margin-left: -9px;
}

.w-widget-twitter-count-shim.w--vertical {
  width: 61px;
  height: 33px;
  margin-bottom: 8px;
}

.w-widget-twitter-count-shim.w--vertical:before, .w-widget-twitter-count-shim.w--vertical:after {
  content: " ";
  pointer-events: none;
  border: solid #0000;
  width: 0;
  height: 0;
  position: absolute;
  top: 100%;
  left: 50%;
}

.w-widget-twitter-count-shim.w--vertical:before {
  border-width: 5px;
  border-color: #5d6c7b #75869600 #75869600;
  margin-left: -5px;
}

.w-widget-twitter-count-shim.w--vertical:after {
  border-width: 4px;
  border-color: #fff #fff0 #fff0;
  margin-left: -4px;
}

.w-widget-twitter-count-shim.w--vertical .w-widget-twitter-count-inner {
  font-size: 18px;
  line-height: 22px;
}

.w-widget-twitter-count-shim.w--vertical.w--large {
  width: 76px;
}

.w-background-video {
  color: #fff;
  height: 500px;
  position: relative;
  overflow: hidden;
}

.w-background-video > video {
  object-fit: cover;
  z-index: -100;
  background-position: 50%;
  background-size: cover;
  width: 100%;
  height: 100%;
  margin: auto;
  position: absolute;
  inset: -100%;
}

.w-background-video > video::-webkit-media-controls-start-playback-button {
  -webkit-appearance: none;
  display: none !important;
}

.w-background-video--control {
  background-color: #0000;
  padding: 0;
  position: absolute;
  bottom: 1em;
  right: 1em;
}

.w-background-video--control > [hidden] {
  display: none !important;
}

.w-slider {
  text-align: center;
  clear: both;
  -webkit-tap-highlight-color: #0000;
  tap-highlight-color: #0000;
  background: #ddd;
  height: 300px;
  position: relative;
}

.w-slider-mask {
  z-index: 1;
  white-space: nowrap;
  height: 100%;
  display: block;
  position: relative;
  left: 0;
  right: 0;
  overflow: hidden;
}

.w-slide {
  vertical-align: top;
  white-space: normal;
  text-align: left;
  width: 100%;
  height: 100%;
  display: inline-block;
  position: relative;
}

.w-slider-nav {
  z-index: 2;
  text-align: center;
  -webkit-tap-highlight-color: #0000;
  tap-highlight-color: #0000;
  height: 40px;
  margin: auto;
  padding-top: 10px;
  position: absolute;
  inset: auto 0 0;
}

.w-slider-nav.w-round > div {
  border-radius: 100%;
}

.w-slider-nav.w-num > div {
  font-size: inherit;
  line-height: inherit;
  width: auto;
  height: auto;
  padding: .2em .5em;
}

.w-slider-nav.w-shadow > div {
  box-shadow: 0 0 3px #3336;
}

.w-slider-nav-invert {
  color: #fff;
}

.w-slider-nav-invert > div {
  background-color: #2226;
}

.w-slider-nav-invert > div.w-active {
  background-color: #222;
}

.w-slider-dot {
  cursor: pointer;
  background-color: #fff6;
  width: 1em;
  height: 1em;
  margin: 0 3px .5em;
  transition: background-color .1s, color .1s;
  display: inline-block;
  position: relative;
}

.w-slider-dot.w-active {
  background-color: #fff;
}

.w-slider-dot:focus {
  outline: none;
  box-shadow: 0 0 0 2px #fff;
}

.w-slider-dot:focus.w-active {
  box-shadow: none;
}

.w-slider-arrow-left, .w-slider-arrow-right {
  cursor: pointer;
  color: #fff;
  -webkit-tap-highlight-color: #0000;
  tap-highlight-color: #0000;
  -webkit-user-select: none;
  user-select: none;
  width: 80px;
  margin: auto;
  font-size: 40px;
  position: absolute;
  inset: 0;
  overflow: hidden;
}

.w-slider-arrow-left [class^="w-icon-"], .w-slider-arrow-right [class^="w-icon-"], .w-slider-arrow-left [class*=" w-icon-"], .w-slider-arrow-right [class*=" w-icon-"] {
  position: absolute;
}

.w-slider-arrow-left:focus, .w-slider-arrow-right:focus {
  outline: 0;
}

.w-slider-arrow-left {
  z-index: 3;
  right: auto;
}

.w-slider-arrow-right {
  z-index: 4;
  left: auto;
}

.w-icon-slider-left, .w-icon-slider-right {
  width: 1em;
  height: 1em;
  margin: auto;
  inset: 0;
}

.w-slider-aria-label {
  clip: rect(0 0 0 0);
  border: 0;
  width: 1px;
  height: 1px;
  margin: -1px;
  padding: 0;
  position: absolute;
  overflow: hidden;
}

.w-slider-force-show {
  display: block !important;
}

.w-dropdown {
  text-align: left;
  z-index: 900;
  margin-left: auto;
  margin-right: auto;
  display: inline-block;
  position: relative;
}

.w-dropdown-btn, .w-dropdown-toggle, .w-dropdown-link {
  vertical-align: top;
  color: #222;
  text-align: left;
  white-space: nowrap;
  margin-left: auto;
  margin-right: auto;
  padding: 20px;
  text-decoration: none;
  position: relative;
}

.w-dropdown-toggle {
  -webkit-user-select: none;
  user-select: none;
  cursor: pointer;
  padding-right: 40px;
  display: inline-block;
}

.w-dropdown-toggle:focus {
  outline: 0;
}

.w-icon-dropdown-toggle {
  width: 1em;
  height: 1em;
  margin: auto 20px auto auto;
  position: absolute;
  top: 0;
  bottom: 0;
  right: 0;
}

.w-dropdown-list {
  background: #ddd;
  min-width: 100%;
  display: none;
  position: absolute;
}

.w-dropdown-list.w--open {
  display: block;
}

.w-dropdown-link {
  color: #222;
  padding: 10px 20px;
  display: block;
}

.w-dropdown-link.w--current {
  color: #0082f3;
}

.w-dropdown-link:focus {
  outline: 0;
}

@media screen and (max-width: 767px) {
  .w-nav-brand {
    padding-left: 10px;
  }
}

.w-lightbox-backdrop {
  cursor: auto;
  letter-spacing: normal;
  text-indent: 0;
  text-shadow: none;
  text-transform: none;
  visibility: visible;
  white-space: normal;
  word-break: normal;
  word-spacing: normal;
  word-wrap: normal;
  color: #fff;
  text-align: center;
  z-index: 2000;
  opacity: 0;
  -webkit-user-select: none;
  -moz-user-select: none;
  -webkit-tap-highlight-color: transparent;
  background: #000000e6;
  outline: 0;
  font-family: Helvetica Neue, Helvetica, Ubuntu, Segoe UI, Verdana, sans-serif;
  font-size: 17px;
  font-style: normal;
  font-weight: 300;
  line-height: 1.2;
  list-style: disc;
  position: fixed;
  inset: 0;
  -webkit-transform: translate(0);
}

.w-lightbox-backdrop, .w-lightbox-container {
  -webkit-overflow-scrolling: touch;
  height: 100%;
  overflow: auto;
}

.w-lightbox-content {
  height: 100vh;
  position: relative;
  overflow: hidden;
}

.w-lightbox-view {
  opacity: 0;
  width: 100vw;
  height: 100vh;
  position: absolute;
}

.w-lightbox-view:before {
  content: "";
  height: 100vh;
}

.w-lightbox-group, .w-lightbox-group .w-lightbox-view, .w-lightbox-group .w-lightbox-view:before {
  height: 86vh;
}

.w-lightbox-frame, .w-lightbox-view:before {
  vertical-align: middle;
  display: inline-block;
}

.w-lightbox-figure {
  margin: 0;
  position: relative;
}

.w-lightbox-group .w-lightbox-figure {
  cursor: pointer;
}

.w-lightbox-img {
  width: auto;
  max-width: none;
  height: auto;
}

.w-lightbox-image {
  float: none;
  max-width: 100vw;
  max-height: 100vh;
  display: block;
}

.w-lightbox-group .w-lightbox-image {
  max-height: 86vh;
}

.w-lightbox-caption {
  text-align: left;
  text-overflow: ellipsis;
  white-space: nowrap;
  background: #0006;
  padding: .5em 1em;
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  overflow: hidden;
}

.w-lightbox-embed {
  width: 100%;
  height: 100%;
  position: absolute;
  inset: 0;
}

.w-lightbox-control {
  cursor: pointer;
  background-position: center;
  background-repeat: no-repeat;
  background-size: 24px;
  width: 4em;
  transition: all .3s;
  position: absolute;
  top: 0;
}

.w-lightbox-left {
  background-image: url("data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9Ii0yMCAwIDI0IDQwIiB3aWR0aD0iMjQiIGhlaWdodD0iNDAiPjxnIHRyYW5zZm9ybT0icm90YXRlKDQ1KSI+PHBhdGggZD0ibTAgMGg1djIzaDIzdjVoLTI4eiIgb3BhY2l0eT0iLjQiLz48cGF0aCBkPSJtMSAxaDN2MjNoMjN2M2gtMjZ6IiBmaWxsPSIjZmZmIi8+PC9nPjwvc3ZnPg==");
  display: none;
  bottom: 0;
  left: 0;
}

.w-lightbox-right {
  background-image: url("data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9Ii00IDAgMjQgNDAiIHdpZHRoPSIyNCIgaGVpZ2h0PSI0MCI+PGcgdHJhbnNmb3JtPSJyb3RhdGUoNDUpIj48cGF0aCBkPSJtMC0waDI4djI4aC01di0yM2gtMjN6IiBvcGFjaXR5PSIuNCIvPjxwYXRoIGQ9Im0xIDFoMjZ2MjZoLTN2LTIzaC0yM3oiIGZpbGw9IiNmZmYiLz48L2c+PC9zdmc+");
  display: none;
  bottom: 0;
  right: 0;
}

.w-lightbox-close {
  background-image: url("data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9Ii00IDAgMTggMTciIHdpZHRoPSIxOCIgaGVpZ2h0PSIxNyI+PGcgdHJhbnNmb3JtPSJyb3RhdGUoNDUpIj48cGF0aCBkPSJtMCAwaDd2LTdoNXY3aDd2NWgtN3Y3aC01di03aC03eiIgb3BhY2l0eT0iLjQiLz48cGF0aCBkPSJtMSAxaDd2LTdoM3Y3aDd2M2gtN3Y3aC0zdi03aC03eiIgZmlsbD0iI2ZmZiIvPjwvZz48L3N2Zz4=");
  background-size: 18px;
  height: 2.6em;
  right: 0;
}

.w-lightbox-strip {
  white-space: nowrap;
  padding: 0 1vh;
  line-height: 0;
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  overflow: auto hidden;
}

.w-lightbox-item {
  box-sizing: content-box;
  cursor: pointer;
  width: 10vh;
  padding: 2vh 1vh;
  display: inline-block;
  -webkit-transform: translate3d(0, 0, 0);
}

.w-lightbox-active {
  opacity: .3;
}

.w-lightbox-thumbnail {
  background: #222;
  height: 10vh;
  position: relative;
  overflow: hidden;
}

.w-lightbox-thumbnail-image {
  position: absolute;
  top: 0;
  left: 0;
}

.w-lightbox-thumbnail .w-lightbox-tall {
  width: 100%;
  top: 50%;
  transform: translate(0, -50%);
}

.w-lightbox-thumbnail .w-lightbox-wide {
  height: 100%;
  left: 50%;
  transform: translate(-50%);
}

.w-lightbox-spinner {
  box-sizing: border-box;
  border: 5px solid #0006;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  margin-top: -20px;
  margin-left: -20px;
  animation: .8s linear infinite spin;
  position: absolute;
  top: 50%;
  left: 50%;
}

.w-lightbox-spinner:after {
  content: "";
  border: 3px solid #0000;
  border-bottom-color: #fff;
  border-radius: 50%;
  position: absolute;
  inset: -4px;
}

.w-lightbox-hide {
  display: none;
}

.w-lightbox-noscroll {
  overflow: hidden;
}

@media (min-width: 768px) {
  .w-lightbox-content {
    height: 96vh;
    margin-top: 2vh;
  }

  .w-lightbox-view, .w-lightbox-view:before {
    height: 96vh;
  }

  .w-lightbox-group, .w-lightbox-group .w-lightbox-view, .w-lightbox-group .w-lightbox-view:before {
    height: 84vh;
  }

  .w-lightbox-image {
    max-width: 96vw;
    max-height: 96vh;
  }

  .w-lightbox-group .w-lightbox-image {
    max-width: 82.3vw;
    max-height: 84vh;
  }

  .w-lightbox-left, .w-lightbox-right {
    opacity: .5;
    display: block;
  }

  .w-lightbox-close {
    opacity: .8;
  }

  .w-lightbox-control:hover {
    opacity: 1;
  }
}

.w-lightbox-inactive, .w-lightbox-inactive:hover {
  opacity: 0;
}

.w-richtext:before, .w-richtext:after {
  content: " ";
  grid-area: 1 / 1 / 2 / 2;
  display: table;
}

.w-richtext:after {
  clear: both;
}

.w-richtext[contenteditable="true"]:before, .w-richtext[contenteditable="true"]:after {
  white-space: initial;
}

.w-richtext ol, .w-richtext ul {
  overflow: hidden;
}

.w-richtext .w-richtext-figure-selected.w-richtext-figure-type-video div:after, .w-richtext .w-richtext-figure-selected[data-rt-type="video"] div:after, .w-richtext .w-richtext-figure-selected.w-richtext-figure-type-image div, .w-richtext .w-richtext-figure-selected[data-rt-type="image"] div {
  outline: 2px solid #2895f7;
}

.w-richtext figure.w-richtext-figure-type-video > div:after, .w-richtext figure[data-rt-type="video"] > div:after {
  content: "";
  display: none;
  position: absolute;
  inset: 0;
}

.w-richtext figure {
  max-width: 60%;
  position: relative;
}

.w-richtext figure > div:before {
  cursor: default !important;
}

.w-richtext figure img {
  width: 100%;
}

.w-richtext figure figcaption.w-richtext-figcaption-placeholder {
  opacity: .6;
}

.w-richtext figure div {
  color: #0000;
  font-size: 0;
}

.w-richtext figure.w-richtext-figure-type-image, .w-richtext figure[data-rt-type="image"] {
  display: table;
}

.w-richtext figure.w-richtext-figure-type-image > div, .w-richtext figure[data-rt-type="image"] > div {
  display: inline-block;
}

.w-richtext figure.w-richtext-figure-type-image > figcaption, .w-richtext figure[data-rt-type="image"] > figcaption {
  caption-side: bottom;
  display: table-caption;
}

.w-richtext figure.w-richtext-figure-type-video, .w-richtext figure[data-rt-type="video"] {
  width: 60%;
  height: 0;
}

.w-richtext figure.w-richtext-figure-type-video iframe, .w-richtext figure[data-rt-type="video"] iframe {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
}

.w-richtext figure.w-richtext-figure-type-video > div, .w-richtext figure[data-rt-type="video"] > div {
  width: 100%;
}

.w-richtext figure.w-richtext-align-center {
  clear: both;
  margin-left: auto;
  margin-right: auto;
}

.w-richtext figure.w-richtext-align-center.w-richtext-figure-type-image > div, .w-richtext figure.w-richtext-align-center[data-rt-type="image"] > div {
  max-width: 100%;
}

.w-richtext figure.w-richtext-align-normal {
  clear: both;
}

.w-richtext figure.w-richtext-align-fullwidth {
  text-align: center;
  clear: both;
  width: 100%;
  max-width: 100%;
  margin-left: auto;
  margin-right: auto;
  display: block;
}

.w-richtext figure.w-richtext-align-fullwidth > div {
  padding-bottom: inherit;
  display: inline-block;
}

.w-richtext figure.w-richtext-align-fullwidth > figcaption {
  display: block;
}

.w-richtext figure.w-richtext-align-floatleft {
  float: left;
  clear: none;
  margin-right: 15px;
}

.w-richtext figure.w-richtext-align-floatright {
  float: right;
  clear: none;
  margin-left: 15px;
}

.w-nav {
  z-index: 1000;
  background: #ddd;
  position: relative;
}

.w-nav:before, .w-nav:after {
  content: " ";
  grid-area: 1 / 1 / 2 / 2;
  display: table;
}

.w-nav:after {
  clear: both;
}

.w-nav-brand {
  float: left;
  color: #333;
  text-decoration: none;
  position: relative;
}

.w-nav-link {
  vertical-align: top;
  color: #222;
  text-align: left;
  margin-left: auto;
  margin-right: auto;
  padding: 20px;
  text-decoration: none;
  display: inline-block;
  position: relative;
}

.w-nav-link.w--current {
  color: #0082f3;
}

.w-nav-menu {
  float: right;
  position: relative;
}

[data-nav-menu-open] {
  text-align: center;
  background: #c8c8c8;
  min-width: 200px;
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  overflow: visible;
  display: block !important;
}

.w--nav-link-open {
  display: block;
  position: relative;
}

.w-nav-overlay {
  width: 100%;
  display: none;
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  overflow: hidden;
}

.w-nav-overlay [data-nav-menu-open] {
  top: 0;
}

.w-nav[data-animation="over-left"] .w-nav-overlay {
  width: auto;
}

.w-nav[data-animation="over-left"] .w-nav-overlay, .w-nav[data-animation="over-left"] [data-nav-menu-open] {
  z-index: 1;
  top: 0;
  right: auto;
}

.w-nav[data-animation="over-right"] .w-nav-overlay {
  width: auto;
}

.w-nav[data-animation="over-right"] .w-nav-overlay, .w-nav[data-animation="over-right"] [data-nav-menu-open] {
  z-index: 1;
  top: 0;
  left: auto;
}

.w-nav-button {
  float: right;
  cursor: pointer;
  -webkit-tap-highlight-color: #0000;
  tap-highlight-color: #0000;
  -webkit-user-select: none;
  user-select: none;
  padding: 18px;
  font-size: 24px;
  display: none;
  position: relative;
}

.w-nav-button:focus {
  outline: 0;
}

.w-nav-button.w--open {
  color: #fff;
  background-color: #c8c8c8;
}

.w-nav[data-collapse="all"] .w-nav-menu {
  display: none;
}

.w-nav[data-collapse="all"] .w-nav-button, .w--nav-dropdown-open, .w--nav-dropdown-toggle-open {
  display: block;
}

.w--nav-dropdown-list-open {
  position: static;
}

@media screen and (max-width: 991px) {
  .w-nav[data-collapse="medium"] .w-nav-menu {
    display: none;
  }

  .w-nav[data-collapse="medium"] .w-nav-button {
    display: block;
  }
}

@media screen and (max-width: 767px) {
  .w-nav[data-collapse="small"] .w-nav-menu {
    display: none;
  }

  .w-nav[data-collapse="small"] .w-nav-button {
    display: block;
  }

  .w-nav-brand {
    padding-left: 10px;
  }
}

@media screen and (max-width: 479px) {
  .w-nav[data-collapse="tiny"] .w-nav-menu {
    display: none;
  }

  .w-nav[data-collapse="tiny"] .w-nav-button {
    display: block;
  }
}

.w-tabs {
  position: relative;
}

.w-tabs:before, .w-tabs:after {
  content: " ";
  grid-area: 1 / 1 / 2 / 2;
  display: table;
}

.w-tabs:after {
  clear: both;
}

.w-tab-menu {
  position: relative;
}

.w-tab-link {
  vertical-align: top;
  text-align: left;
  cursor: pointer;
  color: #222;
  background-color: #ddd;
  padding: 9px 30px;
  text-decoration: none;
  display: inline-block;
  position: relative;
}

.w-tab-link.w--current {
  background-color: #c8c8c8;
}

.w-tab-link:focus {
  outline: 0;
}

.w-tab-content {
  display: block;
  position: relative;
  overflow: hidden;
}

.w-tab-pane {
  display: none;
  position: relative;
}

.w--tab-active {
  display: block;
}

@media screen and (max-width: 479px) {
  .w-tab-link {
    display: block;
  }
}

.w-ix-emptyfix:after {
  content: "";
}

@keyframes spin {
  0% {
    transform: rotate(0);
  }

  100% {
    transform: rotate(360deg);
  }
}

.w-dyn-empty {
  background-color: #ddd;
  padding: 10px;
}

.w-dyn-hide, .w-dyn-bind-empty, .w-condition-invisible {
  display: none !important;
}

.wf-layout-layout {
  display: grid;
}

:root {
  --color--white: white;
  --font--inter: Inter, sans-serif;
  --color--black2: #1a1a1a;
  --color--black4: #09090b;
  --color--black: #000;
  --color--black3: #404040;
  --color--stroke2: #d4d4d4;
  --color--stroke1: #e7e7e7;
  --color--bg1: #f8f9fa;
  --color--bg2: #f8f8f8;
  --font--instrument-serif: "Instrument Serif", sans-serif;
}

.w-layout-blockcontainer {
  max-width: 940px;
  margin-left: auto;
  margin-right: auto;
  display: block;
}

@media screen and (max-width: 991px) {
  .w-layout-blockcontainer {
    max-width: 728px;
  }
}

@media screen and (max-width: 767px) {
  .w-layout-blockcontainer {
    max-width: none;
  }
}

h1 {
  letter-spacing: -2px;
  margin-top: 0;
  margin-bottom: 0;
  font-size: 64px;
  font-weight: 400;
  line-height: 72px;
}

h2 {
  letter-spacing: -2px;
  margin-top: 0;
  margin-bottom: 0;
  font-size: 56px;
  font-weight: 500;
  line-height: 70px;
}

h3 {
  margin-top: 0;
  margin-bottom: 0;
  font-size: 36px;
  font-weight: 500;
  line-height: 44px;
}

h4 {
  margin-top: 0;
  margin-bottom: 0;
  font-size: 32px;
  font-weight: 500;
  line-height: 38px;
}

p {
  margin-bottom: 10px;
}

.body {
  background-color: var(--color--white);
  font-family: var(--font--inter);
  color: var(--color--black2);
  font-size: 16px;
  line-height: 20px;
}

.page-wrapper {
  overflow: clip;
}

.nav-wrapper {
  z-index: 9999;
  justify-content: center;
  align-items: flex-start;
  padding-top: 0px;
  padding-left: 0px;
  padding-right: 0px;
  display: flex;
  position: sticky;
  top: 40px;
  width: content;
  border-radius: 100px;
  margin-left: auto;
  margin-right: auto;
  background-color: #1a1c1d;
}

.navbar {
  background-color: #1a1c1d;
  border-radius: 100px;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  display: flex;
}

.nav-content {
  grid-column-gap: 40px;
  grid-row-gap: 40px;
  grid-template-rows: auto;
  grid-template-columns: .25fr 1fr .25fr;
  grid-auto-columns: 1fr;
  justify-content: space-between;
  align-items: center;
  display: flex;
}

.nav-menu {
  display: flex;
}

.nav-link {
  color: var(--color--white);
  letter-spacing: -.13px;
  padding: 4px 14px;
  font-size: 15px;
  font-weight: 500;
  text-decoration: none;
}

.nav-log {
  border-radius: 100%;
  width: 100px;
  height: 60px;
  object-fit: contain;
}

.nav-button {
  background-color: var(--color--white);
  color: var(--color--black4);
  letter-spacing: -.13px;
  border-radius: 100px;
  padding: 10px 18px;
  font-size: 15px;
  font-weight: 500;
  text-decoration: none;
  transition: all .3s;
}

.nav-button:hover {
  transform: scale(.96);
}

.nav-link-wrap {
  flex-flow: column;
  justify-content: flex-start;
  align-items: center;
  display: flex;
}

.nav-das {
  background-color: var(--color--white);
  border-radius: 100%;
  width: 4px;
  height: 4px;
  position: absolute;
  inset: auto auto 0%;
  transform: scale(0);
}

.hero-section {
  padding-top: 100px;
  padding-bottom: 100px;
}

.container {
  width: 100%;
  max-width: 1290px;
  margin-left: auto;
  margin-right: auto;
  padding-left: 20px;
  padding-right: 20px;
}

.hero-content-wrapper, .hero-top-content {
  flex-flow: column;
  justify-content: flex-start;
  align-items: center;
  display: flex;
}

.hero-top-info-wrap {
  grid-column-gap: 20px;
  grid-row-gap: 20px;
  flex-flow: column;
  justify-content: flex-start;
  align-items: center;
  display: flex;
}

.hero-top-info-title {
  color: var(--color--black);
  letter-spacing: -.72px;
  font-size: 18px;
  line-height: 28px;
}

.hero-info-title-wrap {
  flex-flow: column;
  justify-content: flex-start;
  align-items: center;
  width: 100%;
  max-width: 664px;
  margin-top: 14px;
  display: flex;
}

.hero-info-title {
  color: var(--color--black);
  text-align: center;
  letter-spacing: -2px;
  margin-top: 0;
  margin-bottom: 0;
  font-size: 64px;
  font-weight: 600;
  line-height: 72px;
}

.hero-info-d {
  text-align: center;
  width: 100%;
  max-width: 90%;
  margin-top: 28px;
}

.hero-bottom-wrapper {
  grid-column-gap: 12px;
  grid-row-gap: 12px;
  justify-content: space-between;
  align-items: center;
  margin-top: 42px;
  display: flex;
}

.button {
  color: var(--color--white);
  letter-spacing: -.32px;
  background-color: #3898ec00;
  background-image: linear-gradient(#4d4d4d, #000);
  border-radius: 50px;
  padding: 16px 24px;
  font-weight: 500;
  text-decoration: none;
  transition: all .3s;
  box-shadow: inset 0 1px 1px #ffffff40;
}

.button:hover {
  transform: scale(.96);
}

.button.strok {
  border: 1px solid var(--color--black2);
  color: var(--color--black);
  background-image: none;
  box-shadow: inset 0 1px 1px #ffffff40;
}

.partners-section {
  padding-bottom: 100px;
}

.partners-content-wrapper {
  flex-flow: column;
  justify-content: flex-start;
  align-items: center;
  display: flex;
}

.partners-title-wrap {
  justify-content: center;
  align-items: flex-start;
}

.partners-title {
  color: var(--color--black3);
}

.partners-log-container {
  grid-column-gap: 36px;
  grid-row-gap: 36px;
  flex: none;
  justify-content: center;
  align-items: center;
  overflow: hidden;
}

.partners-log-container.t {
  display: none;
}

.showcase-section {
  padding-bottom: 100px;
}

.showcase-wrap {
  grid-column-gap: 18px;
  grid-row-gap: 18px;
  justify-content: flex-start;
  align-items: center;
  display: flex;
}

.showcase-item-wrapper {
  grid-column-gap: 18px;
  grid-row-gap: 18px;
  grid-template-rows: auto;
  grid-template-columns: 1fr 1fr 1fr;
  grid-auto-columns: 1fr;
  justify-content: flex-start;
  align-items: center;
  display: flex;
}

.showcase-item {
  width: 100%;
  height: 418px;
}

.showcase-item-imge {
  border-radius: 8px;
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.showcase-content {
  flex: none;
}

.showcase-content-wrapper {
  padding-left: 20px;
  padding-right: 20px;
  position: relative;
  overflow: hidden;
}

.about-section {
  padding-bottom: 100px;
}

.about-top-content {
  grid-column-gap: 70px;
  grid-row-gap: 70px;
  flex-flow: row;
  justify-content: space-between;
  align-items: stretch;
  display: flex;
}

.title {
  color: var(--color--black);
  text-align: center;
  font-size: 18px;
}

.about-right-content {
  grid-column-gap: 32px;
  grid-row-gap: 32px;
  flex-flow: column;
  width: 100%;
  max-width: 674px;
  display: flex;
}

.about-right-descption {
  color: var(--color--black4);
  letter-spacing: -.6px;
  margin-bottom: 0;
  font-size: 24px;
  line-height: 31px;
}

.service-section {
  padding-top: 100px;
  padding-bottom: 100px;
}

.title-content-wrapper {
  flex-flow: column;
  justify-content: flex-start;
  align-items: center;
  display: flex;
}

.sub-title {
  border: 1px solid var(--color--stroke2);
  border-radius: 50px;
  justify-content: center;
  align-items: center;
  padding: 7px 13px;
  display: flex;
}

.sub-title-text {
  font-size: 14px;
  line-height: 19px;
}

.title-wrap {
  width: 100%;
  max-width: 695px;
  margin-top: 18px;
}

.title-descption-wrap {
  width: 100%;
  max-width: 446px;
  margin-top: 18px;
}

.title-descption {
  color: var(--color--black3);
  text-align: center;
  letter-spacing: -.72px;
  font-size: 18px;
  line-height: 28px;
}

.service-itme-content {
  grid-column-gap: 24px;
  grid-row-gap: 24px;
  flex-flow: column;
  margin-top: 80px;
  display: flex;
}

.service-itme {
  border: 1px solid var(--color--stroke1);
  background-color: var(--color--bg1);
  cursor: pointer;
  border-radius: 18px;
  flex-flow: column;
  justify-content: space-between;
  align-items: stretch;
  padding: 28px;
  transition: all .3s;
  display: flex;
}

.service-itme:hover {
  transform: scale(1.01);
}

.service-itme-top {
  justify-content: space-between;
  align-items: center;
  width: 100%;
  margin-left: 0;
  margin-right: 0;
  display: flex;
}

.service-itme-top-left {
  grid-column-gap: 14px;
  grid-row-gap: 14px;
  justify-content: flex-start;
  align-items: center;
  display: flex;
}

.service-itme-top-right {
  justify-content: space-between;
  align-items: center;
  width: 100%;
  max-width: 622px;
  display: flex;
}

.service-right-button {
  color: var(--color--white);
  letter-spacing: -.32px;
  background-color: #3898ec00;
  background-image: linear-gradient(#4d4d4d, #000);
  border-radius: 50px;
  padding: 13px 24px;
  font-weight: 500;
  text-decoration: none;
  box-shadow: inset 0 1px 1px #ffffff40;
}

.service-list-itme {
  width: 100%;
  max-width: 622px;
  margin-top: 28px;
  margin-left: auto;
}

.service-imge {
  border-radius: 15px;
  width: 100%;
}

.servuce-list-descption {
  color: #45454a;
  letter-spacing: -.5px;
  margin-top: 28px;
  font-size: 18px;
  line-height: 22px;
}

.service-item-wrapper {
  grid-column-gap: 28px;
  grid-row-gap: 28px;
  flex-flow: column;
  display: flex;
  position: relative;
}

.work-process-seciton {
  background-color: var(--color--bg1);
  color: var(--color--black);
  padding-top: 120px;
  padding-bottom: 120px;
  font-size: 20px;
  font-weight: 500;
  line-height: 24px;
}

.work-section {
  padding-top: 100px;
  padding-bottom: 100px;
}

.work-content {
  margin-top: 60px;
}

.work-grid {
  grid-column-gap: 20px;
  grid-row-gap: 20px;
  grid-template-rows: auto;
  grid-template-columns: 1fr 1fr;
  grid-auto-columns: 1fr;
  display: grid;
}

.work-card {
  border: 1px solid var(--color--stroke2);
  border-radius: 18px;
  padding: 8px 10px 28px;
}

.work-image-wrap {
  border-radius: 15px;
  text-decoration: none;
  position: relative;
  overflow: hidden;
}

.work-imge {
  transition: all .4s;
}

.work-imge:hover {
  transform: scale(1.06);
}

.work-info-wrap {
  margin-top: 28px;
  padding-left: 11px;
  padding-right: 11px;
}

.work-info-top {
  justify-content: space-between;
  align-items: center;
  display: flex;
}

.work-name {
  color: var(--color--black4);
  letter-spacing: -.5px;
  text-transform: uppercase;
  font-size: 24px;
  font-weight: 500;
  line-height: 28px;
}

.work-date {
  color: var(--color--black4);
  letter-spacing: -.5px;
  font-size: 20px;
  font-weight: 500;
  line-height: 24px;
}

.work-descption-wrap {
  margin-top: 18px;
}

.paragraph {
  color: #45454a;
  letter-spacing: -.5px;
  margin-bottom: 0;
  font-size: 18px;
  line-height: 28px;
}

.pricing-section {
  background-color: var(--color--bg1);
  padding-top: 120px;
  padding-bottom: 120px;
}

.pricing-content {
  grid-column-gap: 24px;
  grid-row-gap: 24px;
  grid-template-rows: auto;
  grid-template-columns: 1fr 1fr 1fr;
  grid-auto-columns: 1fr;
  margin-top: 60px;
  display: grid;
}

.pricing-card {
  border: 1px solid var(--color--stroke1);
  background-color: var(--color--white);
  border-radius: 12px;
  flex-flow: column;
  padding: 24px 20px;
  display: flex;
}

.pricing-card._2 {
  border-color: var(--color--black);
}

.pricing-title-wrap {
  justify-content: space-between;
  align-items: center;
}

.pricing-title {
  color: var(--color--black4);
  font-size: 20px;
  font-weight: 500;
  line-height: 24px;
}

.price-wrapper {
  grid-column-gap: 6px;
  grid-row-gap: 6px;
  justify-content: flex-start;
  align-items: center;
  margin-top: 16px;
  display: flex;
}

.price {
  color: var(--color--black4);
  font-weight: 600;
  line-height: 38px;
}

.price-label {
  color: var(--color--black4);
}

.pricing-d-wrap {
  margin-top: 16px;
}

.pricing-d {
  color: var(--color--black3);
  font-size: 18px;
  line-height: 26px;
}

.pricing-button {
  color: var(--color--white);
  letter-spacing: -.32px;
  background-color: #3898ec00;
  background-image: linear-gradient(#4d4d4d, #000);
  border-radius: 50px;
  justify-content: center;
  align-items: center;
  width: 100%;
  margin-top: 40px;
  padding: 14px 24px;
  font-weight: 500;
  text-decoration: none;
  transition: all .3s;
  display: flex;
  box-shadow: inset 0 1px 1px #ffffff40;
}

.pricing-button:hover {
  transform: scale(.96);
}

.pricing-button.strok {
  border: 1px solid var(--color--black2);
  color: var(--color--black);
  background-image: none;
  margin-top: 40px;
  padding-top: 14px;
  padding-bottom: 14px;
  transition: all .3s;
  box-shadow: inset 0 1px 1px #ffffff40;
}

.pricing-button.strok:hover {
  transform: scale(.96);
}

.pricing-card-bottom {
  margin-top: 40px;
}

.price-feature-list-wrapper {
  grid-column-gap: 20px;
  grid-row-gap: 20px;
  flex-flow: column;
  display: flex;
}

.price-feature-list {
  grid-column-gap: 12px;
  grid-row-gap: 12px;
  justify-content: flex-start;
  align-items: center;
  display: flex;
}

.price-feature-list-text {
  font-size: 17px;
  line-height: 22px;
}

.testimonial-section {
  padding-top: 100px;
  padding-bottom: 100px;
}

.testimonial-content-wrapper {
  padding-left: 30px;
  padding-right: 30px;
}

.testimonial-content {
  grid-column-gap: 24px;
  grid-row-gap: 24px;
  grid-template-rows: auto;
  grid-template-columns: 1fr 1fr 1fr;
  grid-auto-columns: 1fr;
  margin-top: 60px;
  display: grid;
}

.testimonial {
  grid-column-gap: 24px;
  grid-row-gap: 24px;
  flex-flow: column;
  display: flex;
}

.testimonial-card {
  background-color: var(--color--bg1);
  border-radius: 16px;
  width: 100%;
  height: 100%;
  padding: 24px;
}

.testimonial-descption-wrapper {
  margin-top: 40px;
}

.testimonial-descption {
  color: var(--color--black4);
  letter-spacing: -.64px;
  margin-bottom: 0;
  font-size: 16px;
  font-weight: 400;
  line-height: 24px;
}

.testimonial-card-divider {
  background-color: var(--color--stroke1);
  width: 100%;
  height: 1px;
  margin-top: 40px;
  margin-bottom: 32px;
}

.testimonial-author-info {
  grid-column-gap: 16px;
  grid-row-gap: 16px;
  justify-content: flex-start;
  align-items: center;
  display: flex;
}

.testimonial-author-imge {
  border-radius: 100%;
}

.author-name {
  color: var(--color--black4);
  letter-spacing: -.64px;
  font-weight: 600;
  line-height: 24px;
}

.author-title {
  color: #8a8a8f;
  letter-spacing: -.6px;
  font-size: 15px;
  line-height: 24px;
}

.faq-section {
  padding-top: 100px;
  padding-bottom: 100px;
}

.faq-content {
  margin-top: 60px;
  padding-left: 180px;
  padding-right: 180px;
}

.faq-item-wrapper {
  grid-column-gap: 12px;
  grid-row-gap: 12px;
  flex-flow: column;
  display: flex;
}

.faq-item {
  background-color: var(--color--bg1);
  border-radius: 16px;
  width: 100%;
  transition: all .3s;
}

.faq-item:hover {
  transform: scale(1.01);
}

.faq-toggle {
  justify-content: space-between;
  align-items: center;
  width: 100%;
  padding: 22px 28px;
  display: flex;
}

.faq-toggle.w--open {
  background-color: #fff0;
}

.faq-icon-wrap {
  justify-content: center;
  align-items: center;
  display: flex;
}

.faq-title {
  color: var(--color--black4);
  letter-spacing: -.8px;
  white-space: normal;
  word-break: normal;
  font-size: 18px;
  font-weight: 500;
}

.faq-list {
  background-color: #ddd0;
  padding: 12px 28px 22px;
  display: none;
  position: relative;
}

.faq-answer {
  color: var(--color--black3);
  letter-spacing: -.5px;
  margin-bottom: 0;
  font-size: 16px;
  font-weight: 500;
  line-height: 24px;
  position: relative;
}

.footer-section {
  padding-bottom: 30px;
  padding-left: 30px;
  padding-right: 30px;
}

.footer-content-wrapper {
  color: var(--color--black4);
  padding-left: 40px;
  padding-right: 40px;
}

.footer-background {
  background-color: var(--color--black4);
  border-radius: 28px;
  padding-top: 60px;
  padding-bottom: 60px;
}

.footer-top-content {
  justify-content: space-between;
  align-items: flex-end;
  display: flex;
}

.footer-title {
  color: var(--color--white);
  letter-spacing: -3px;
  margin-top: 0;
  margin-bottom: 0;
  font-family: Playfair Display, sans-serif;
  font-size: 90px;
  font-weight: 400;
  line-height: 120px;
}

.text-span {
  color: #ffffffde;
  font-style: italic;
  font-weight: 400;
}

.footer-sub-title {
  color: #c9c9c9;
  padding-left: 3px;
}

.footer-s-link {
  color: #ffffffe3;
  font-weight: 400;
  line-height: 38px;
}

.footer-s-link-wrap {
  flex-flow: column;
  justify-content: flex-start;
  align-items: stretch;
  margin-top: 40px;
  display: inline-flex;
}

.footer-line {
  background-color: #ffffffb3;
  width: 100%;
  height: 1px;
  margin-top: 8px;
}

.footer-top-right {
  grid-column-gap: 12px;
  grid-row-gap: 12px;
  justify-content: flex-start;
  align-items: center;
  display: flex;
}

.social-link {
  border: 1px solid var(--color--stroke1);
  border-radius: 100%;
  justify-content: center;
  align-items: center;
  padding: 8px;
  text-decoration: none;
  transition: all .3s;
  display: flex;
}

.social-link:hover {
  transform: translate(0, -7px);
}

.social-link-icon {
  width: 20px;
  height: 20px;
}

.footer-bottom-content {
  border-top: 1px solid #fff9;
  justify-content: space-between;
  align-items: center;
  margin-top: 80px;
  padding-top: 28px;
  display: flex;
}

.footer-bottom-text-link {
  color: #ffffffb8;
  text-decoration: none;
  transition: all .3s;
}

.footer-bottom-text-link:hover {
  color: var(--color--white);
}

.link {
  color: var(--color--white);
  text-decoration: none;
}

.utility-page-wrap {
  justify-content: center;
  align-items: center;
  width: 100vw;
  max-width: 100%;
  height: 100vh;
  max-height: 100%;
  display: flex;
}

.utility-page-content {
  text-align: center;
  flex-direction: column;
  width: 260px;
  display: flex;
}

.utility-page-form {
  flex-direction: column;
  align-items: stretch;
  display: flex;
}

.title-p {
  text-align: center;
  text-transform: capitalize;
  font-weight: 600;
}

.contact-pop-up-wrapper {
  z-index: 999999;
  -webkit-backdrop-filter: blur(7px);
  backdrop-filter: blur(7px);
  background-color: #0000002e;
  justify-content: center;
  align-items: center;
  display: none;
  position: fixed;
  inset: 0%;
}

.contact-pop-up {
  background-color: var(--color--bg1);
  border-radius: 20px;
  justify-content: center;
  align-items: center;
  width: 100%;
  max-width: 70%;
  height: 86%;
  padding: 40px;
  display: flex;
  position: relative;
}

.contact-pop-up-close-icon-wrap {
  cursor: pointer;
  position: absolute;
  inset: 40px 40px auto auto;
}

.contact-pop-up-close-icon {
  width: 24px;
}

.contact-pop-up-form-wrap {
  flex-flow: column;
  justify-content: flex-start;
  align-items: center;
  display: flex;
}

.contact-pop-up-form-block {
  margin-bottom: 0;
}

.contact-pop-up-form {
  flex-flow: column;
  justify-content: flex-start;
  align-items: flex-start;
  margin-top: 40px;
  display: flex;
}

.contact-pop-up-title-wrap {
  justify-content: center;
  align-items: flex-start;
  display: flex;
}

.contact-pop-up-title {
  text-align: center;
  width: 100%;
  max-width: 70%;
  font-weight: 500;
  line-height: 40px;
}

.contact-text-field {
  border: 1px solid var(--color--stroke2);
  background-color: var(--color--white);
  border-radius: 8px;
  margin-bottom: 20px;
  padding: 22px 16px;
}

.contact-text-field:focus {
  border-color: #0006;
}

.contact-text-field::placeholder {
  color: #00000080;
}

.contact-text-field.area {
  padding-bottom: 80px;
}

.success-message {
  background-color: var(--color--black4);
  color: var(--color--white);
  border-radius: 6px;
  margin-top: 20px;
}

.error-message {
  background-color: #f8cbcb;
  border-radius: 4px;
  margin-top: 20px;
  padding: 16px;
}

.cursor-wrapper {
  z-index: 99999999;
  pointer-events: none;
  mix-blend-mode: difference;
  justify-content: center;
  align-items: center;
  display: flex;
  position: fixed;
  inset: 0%;
}

.cursor {
  background-color: var(--color--white);
  border-radius: 100%;
  justify-content: center;
  align-items: center;
  width: 14px;
  height: 14px;
  display: flex;
  transform: scale(0);
}

.partners-log-wrapper {
  justify-content: flex-start;
  align-items: center;
  margin-top: 20px;
  display: flex;
  overflow: hidden;
}

.p-wrap {
  grid-column-gap: 36px;
  grid-row-gap: 36px;
  justify-content: flex-start;
  align-items: center;
  display: flex;
}

.templates-info-hero {
  padding-top: 100px;
  padding-bottom: 80px;
}

.templates-info-hero-contetn-wrappr {
  flex-flow: column;
  justify-content: flex-start;
  align-items: center;
  display: flex;
}

.info-hero-top-sub-title-wrap {
  border: 1px solid var(--color--stroke1);
  border-radius: 100px;
  justify-content: center;
  align-items: center;
  padding: 6px 16px;
  display: flex;
}

.info-hero-top-sub-title {
  font-size: 12px;
}

.info-hero-descption {
  color: var(--color--black4);
  text-align: center;
  width: 100%;
  max-width: 43%;
  margin-top: 20px;
  margin-bottom: 0;
}

.style-guide-section {
  background-color: var(--color--bg1);
  margin-bottom: 100px;
  padding-top: 80px;
  padding-bottom: 100px;
}

.style-guide-content-wrapper {
  justify-content: space-between;
  align-items: flex-start;
  display: flex;
}

.templates-nav-block {
  grid-column-gap: 20px;
  grid-row-gap: 20px;
  border: 1px solid var(--color--stroke2);
  background-color: var(--color--white);
  border-radius: 14px;
  flex-flow: column;
  justify-content: flex-start;
  align-items: stretch;
  width: 100%;
  max-width: 310px;
  padding: 24px;
  display: flex;
  position: sticky;
  top: 20px;
  box-shadow: 0 4px 6px -2px #10182808, 0 12px 16px -4px #10182814;
}

.templates-nav-link {
  color: var(--color--black);
  border-radius: 6px;
  padding: 14px;
  font-size: 18px;
  font-weight: 500;
  line-height: 21px;
  text-decoration: none;
  transition: all .3s;
}

.templates-nav-link:hover, .templates-nav-link.w--current {
  background-color: var(--color--bg2);
}

.template-content-blocks-wrapper {
  grid-column-gap: 58px;
  grid-row-gap: 58px;
  flex-flow: column;
  width: 100%;
  max-width: 69%;
  display: flex;
}

.templates-content-block {
  border: 1px solid var(--color--stroke2);
  background-color: var(--color--white);
  border-radius: 24px;
  padding: 32px;
}

.style-title-wrap {
  background-color: var(--color--bg1);
  border-radius: 16px;
  justify-content: flex-start;
  align-items: center;
  padding: 24px;
  display: flex;
}

.style-title-wrap._1 {
  justify-content: space-between;
}

.style-title.r {
  font-size: 20px;
  font-weight: 500;
  line-height: 24px;
}

.colors-wrapper {
  grid-column-gap: 16px;
  grid-row-gap: 16px;
  grid-template-rows: auto;
  grid-template-columns: 1fr 1fr 1fr;
  grid-auto-columns: 1fr;
  margin-top: 60px;
  display: grid;
}

.color-block {
  border: 1px solid var(--color--stroke2);
  background-color: var(--color--white);
  border-radius: 16px;
  width: 100%;
  height: 160px;
  box-shadow: 0 12px 16px -2px #10182814;
}

.color-block._2 {
  background-color: var(--color--black);
}

.color-block._3 {
  background-color: var(--color--black4);
}

.color-block._4 {
  background-color: var(--color--black3);
}

.color-block._5 {
  background-color: var(--color--black2);
}

.color-block._6 {
  background-color: var(--color--bg1);
}

.color-block._7 {
  background-color: var(--color--bg2);
}

.color-block._8 {
  background-color: var(--color--stroke1);
}

.color-block._9 {
  background-color: var(--color--stroke2);
}

.color-code {
  margin-top: 10px;
  font-size: 16px;
}

.typography-item-block {
  grid-column-gap: 12px;
  grid-row-gap: 12px;
  border-bottom: 1px solid var(--color--stroke1);
  flex-flow: column;
  padding: 32px 24px;
  display: flex;
}

.text-block {
  font-size: 18px;
  line-height: 22px;
}

.text-large {
  letter-spacing: -.5px;
  font-size: 24px;
  font-weight: 500;
  line-height: 32px;
}

.text-medium {
  letter-spacing: -.5px;
  font-size: 18px;
  font-weight: 400;
  line-height: 22px;
}

.text-small {
  letter-spacing: -.5px;
  font-size: 16px;
  font-weight: 400;
  line-height: 20px;
}

.button-block {
  grid-column-gap: 16px;
  grid-row-gap: 16px;
  justify-content: flex-start;
  align-items: center;
  margin-top: 60px;
  display: flex;
}

.info-hero-title {
  margin-top: 24px;
}

.licenses-section {
  background-color: var(--color--bg1);
  margin-bottom: 100px;
  padding-top: 100px;
  padding-bottom: 100px;
}

.licenses-contetn-wrapper {
  flex-flow: column;
  display: flex;
}

.license-content-gird {
  grid-column-gap: 16px;
  grid-row-gap: 16px;
  grid-template-rows: auto;
  grid-template-columns: 1fr 1fr 1fr;
  grid-auto-columns: 1fr;
  display: grid;
}

.license-card {
  border: 1px solid var(--color--stroke1);
  background-color: var(--color--white);
  border-radius: 20px;
  flex-flow: column;
  justify-content: flex-start;
  align-items: stretch;
  width: 100%;
  height: 100%;
  padding: 24px;
  display: flex;
}

.license-card-title {
  color: var(--color--black);
  font-size: 26px;
  font-weight: 500;
  line-height: 30px;
}

.license-card-descption {
  margin-top: 60px;
  margin-bottom: 0;
  line-height: 24px;
}

.link-2, .link-3, .link-5 {
  text-decoration: none;
}

.changelog-section {
  padding-top: 100px;
  padding-bottom: 100px;
}

.changelog-contetn-wrapper {
  background-color: var(--color--bg1);
  border-radius: 20px;
  padding: 48px;
}

.changelog-title {
  margin-bottom: 20px;
}

.changelog-descption {
  margin-bottom: 0;
}

.footer-bottolink-wrap {
  grid-column-gap: 20px;
  grid-row-gap: 20px;
  align-items: center;
  display: flex;
}

.work-experience-section {
  background-color: var(--color--bg1);
  padding-top: 120px;
  padding-bottom: 120px;
}

.work-experience-top-contetn {
  flex-flow: column;
  justify-content: flex-start;
  align-items: center;
  display: flex;
}

.work-experience-top-title {
  font-family: var(--font--instrument-serif);
  color: var(--color--black);
  font-style: italic;
  font-weight: 400;
  line-height: 46px;
}

.work-experiencec-top-descption {
  color: var(--color--black4);
  text-align: center;
  letter-spacing: -.3px;
  width: 100%;
  max-width: 650px;
  margin-top: 24px;
  margin-bottom: 0;
  line-height: 26px;
}

.work-experience-bottom-contetn {
  grid-column-gap: 30px;
  grid-row-gap: 30px;
  flex-flow: column;
  margin-top: 60px;
  display: flex;
}

.work-experience-contetn {
  border-bottom: 1px dashed var(--color--stroke2);
  justify-content: space-between;
  align-items: center;
  padding-top: 12px;
  padding-bottom: 16px;
  display: flex;
}

.work-e-left {
  width: 10%;
}

.work-left-title {
  color: var(--color--black);
  font-weight: 500;
}

.work-e-right {
  grid-column-gap: 13px;
  grid-row-gap: 13px;
  flex-flow: column;
  justify-content: flex-start;
  align-items: flex-end;
  width: 10%;
  display: flex;
}

.work-e-right.m {
  justify-content: flex-start;
  align-items: center;
  width: auto;
}

.work-e-title {
  color: var(--color--black);
  font-weight: 500;
}

.work-e-title.n {
  display: none;
}

.service-top-left-title-2 {
  color: #09090b;
  font-size: 22px;
  line-height: 27px;
}

.service-right-title-2 {
  color: #09090b;
  margin-top: 0;
  margin-bottom: 0;
  font-size: 32px;
  font-weight: 500;
  line-height: 38px;
}

.service-right-button-2 {
  color: #fff;
  letter-spacing: -.32px;
  background-color: #3898ec00;
  background-image: linear-gradient(#4d4d4d, #000);
  border-radius: 50px;
  padding: 13px 24px;
  font-weight: 500;
  text-decoration: none;
  box-shadow: inset 0 1px 1px #ffffff40;
}

.service-right-button-2.n {
  display: none;
}

.service-item-bottom {
  position: relative;
  overflow: hidden;
}

.work-process-content {
  grid-column-gap: 20px;
  grid-row-gap: 20px;
  grid-template-rows: auto;
  grid-template-columns: 1fr 1fr 1fr 1fr;
  grid-auto-columns: 1fr;
  margin-top: 60px;
  display: grid;
}

.work-process-card {
  border: 1px solid var(--color--stroke1);
  background-color: var(--color--white);
  border-radius: 12px;
  flex-flow: column;
  justify-content: flex-start;
  align-items: flex-start;
  padding: 20px;
  transition: all .3s;
  display: flex;
}

.work-process-card:hover {
  transform: scale(1.06);
  box-shadow: 2px 2px 7px #72727233;
}

.work-process-top-icon-wrap {
  background-color: var(--color--white);
  border-radius: 100%;
  justify-content: center;
  align-items: center;
  width: 50px;
  height: 50px;
  padding: 6px;
  display: flex;
  overflow: hidden;
  box-shadow: 0 0 7px #0003;
}

.work-process-top-icon {
  width: 36px;
  height: 36px;
}

.work-process-card-bottom-content {
  margin-top: 60px;
}

.work-process-title {
  letter-spacing: -.5px;
  font-size: 16px;
  font-weight: 600;
  line-height: 20px;
}

.work-process-descption {
  color: var(--color--black3);
  letter-spacing: -.5px;
  margin-top: 20px;
  margin-bottom: 0;
  font-size: 14px;
  line-height: 20px;
}

.link-6 {
  text-decoration: none;
}

.purchase-badge {
  z-index: 2147483647;
  background-color: var(--color--bg1);
  color: #000;
  border-radius: 100px;
  justify-content: center;
  align-items: center;
  padding: 7px 16px;
  text-decoration: none;
  display: flex;
  position: fixed;
  inset: auto 24px 24px auto;
  box-shadow: 0 0 8px #0003;
}

.cursor-text {
  font-size: 14px;
  line-height: 16px;
}

@media screen and (min-width: 1280px) {
  .testimonial-content-wrapper {
    padding-left: 50px;
    padding-right: 50px;
  }
}

@media screen and (max-width: 991px) {
  .nav-wrapper {
    padding-top: 28px;
  }

  .nav-brand-log {
    padding-left: 0;
  }

  .hero-section {
    padding-top: 80px;
    padding-bottom: 80px;
  }

  .hero-info-title-wrap {
    margin-top: 12px;
  }

  .hero-info-title {
    letter-spacing: -1.8px;
    font-size: 50px;
    line-height: 57px;
  }

  .hero-info-d {
    margin-top: 20px;
  }

  .hero-bottom-wrapper {
    margin-top: 36px;
  }

  .partners-section {
    padding-bottom: 70px;
  }

  .partners-log-container.t {
    display: block;
  }

  .partners-log {
    width: 126px;
  }

  .showcase-section {
    padding-bottom: 70px;
  }

  .showcase-wrap, .showcase-item-wrapper {
    grid-column-gap: 16px;
    grid-row-gap: 16px;
  }

  .showcase-item {
    height: 300px;
  }

  .about-section {
    padding-bottom: 60px;
  }

  .about-top-content {
    grid-column-gap: 40px;
    grid-row-gap: 40px;
    flex-flow: column;
    align-items: flex-start;
  }

  .about-right-content {
    max-width: 100%;
  }

  .service-section {
    padding-top: 60px;
    padding-bottom: 60px;
  }

  .title-wrap {
    max-width: 80%;
    margin-top: 16px;
  }

  .title-descption-wrap {
    margin-top: 16px;
  }

  .service-itme-content {
    grid-column-gap: 20px;
    grid-row-gap: 20px;
    margin-top: 60px;
  }

  .service-itme {
    border-radius: 14px;
    padding: 24px;
  }

  .service-itme-top-right {
    max-width: 60%;
  }

  .service-left-icon {
    width: 24px;
  }

  .service-right-button {
    font-size: 15px;
  }

  .service-list-itme {
    max-width: 100%;
    margin-left: 0;
  }

  .work-process-seciton {
    padding-top: 70px;
    padding-bottom: 60px;
  }

  .work-section {
    padding-top: 60px;
    padding-bottom: 60px;
  }

  .work-grid {
    grid-column-gap: 16px;
    grid-row-gap: 16px;
  }

  .work-card {
    border-radius: 16px;
    padding-top: 6px;
    padding-left: 8px;
    padding-right: 8px;
  }

  .work-imge {
    border-radius: 12px;
  }

  .work-name {
    font-size: 22px;
    line-height: 26px;
  }

  .paragraph {
    font-size: 18px;
  }

  .pricing-section {
    padding-top: 60px;
    padding-bottom: 60px;
  }

  .pricing-content {
    grid-column-gap: 20px;
    grid-row-gap: 20px;
    grid-template-columns: 1fr 1fr;
  }

  .testimonial-section {
    padding-top: 70px;
    padding-bottom: 70px;
  }

  .testimonial-content-wrapper {
    padding-left: 0;
    padding-right: 0;
  }

  .testimonial-content {
    flex-flow: column;
    grid-template-rows: auto;
    grid-template-columns: 1fr 1fr;
    grid-auto-flow: row;
    display: flex;
  }

  .testimonial {
    grid-column-gap: 24px;
    grid-row-gap: 24px;
    grid-template-rows: auto;
    grid-template-columns: 1fr 1fr;
    grid-auto-columns: 1fr;
    display: grid;
  }

  .faq-section {
    padding-top: 70px;
    padding-bottom: 70px;
  }

  .faq-content {
    margin-top: 50px;
    padding-left: 0;
    padding-right: 0;
  }

  .footer-section {
    padding-bottom: 20px;
    padding-left: 0;
    padding-right: 0;
  }

  .footer-background {
    border-radius: 22px;
    padding-top: 50px;
    padding-bottom: 50px;
  }

  .footer-top-content {
    grid-column-gap: 40px;
    grid-row-gap: 40px;
    flex-flow: column;
    justify-content: space-between;
    align-items: stretch;
  }

  .footer-title {
    font-size: 80px;
    line-height: 90px;
  }

  .footer-s-link {
    font-size: 29px;
    line-height: 37px;
  }

  .title-p {
    font-size: 46px;
    line-height: 60px;
  }

  .contact-pop-up-wrapper {
    display: none;
  }

  .contact-pop-up {
    max-width: 90%;
    height: 80%;
  }

  .cursor-wrapper {
    display: none;
  }

  .partners-log-wrapper, .p-wrap {
    grid-column-gap: 70px;
    grid-row-gap: 70px;
  }

  .templates-info-hero {
    padding-top: 90px;
  }

  .style-guide-section {
    margin-bottom: 80px;
  }

  .style-guide-content-wrapper {
    grid-column-gap: 60px;
    grid-row-gap: 60px;
    flex-flow: column;
    align-items: stretch;
  }

  .templates-nav-block {
    max-width: 100%;
    position: static;
  }

  .template-content-blocks-wrapper {
    grid-column-gap: 40px;
    grid-row-gap: 40px;
    max-width: 100%;
  }

  .text-block {
    font-size: 16px;
    line-height: 20px;
  }

  .heading {
    font-size: 52px;
  }

  .heading-2 {
    letter-spacing: -1px;
    font-size: 48px;
    font-weight: 400;
    line-height: 65px;
  }

  .heading-3 {
    font-size: 33px;
    line-height: 42px;
  }

  .heading-4 {
    font-size: 30px;
  }

  .licenses-section {
    margin-bottom: 80px;
    padding-top: 80px;
    padding-bottom: 80px;
  }

  .license-content-gird {
    grid-template-columns: 1fr 1fr;
  }

  .changelog-contetn-wrapper {
    padding-top: 30px;
    padding-bottom: 30px;
    padding-left: 20px;
  }

  .changelog-title {
    font-size: 50px;
    line-height: 62px;
  }

  .work-experience-section {
    padding-top: 80px;
    padding-bottom: 80px;
  }

  .work-experience-top-title {
    text-align: center;
    font-size: 30px;
    line-height: 42px;
  }

  .work-experience-bottom-contetn {
    margin-top: 50px;
  }

  .work-e-left, .work-e-right {
    width: 14%;
  }

  .work-e-title.n {
    display: none;
  }

  .service-top-left-title-2 {
    font-size: 20px;
  }

  .service-right-title-2 {
    font-size: 26px;
    line-height: 32px;
  }

  .service-right-button-2 {
    font-size: 15px;
  }

  .work-process-content {
    grid-template-columns: 1fr 1fr;
  }
}

@media screen and (max-width: 767px) {
  .nav-wrapper {
    padding-top: 24px;
  }

  .navbar {
    width: 100%;
    padding: 14px;
  }

  .nav-content {
    width: 100%;
  }

  .nav-menu-wrapper {
    display: none;
  }

  .hero-section {
    padding-top: 70px;
    padding-bottom: 70px;
  }

  .container {
    padding-left: 15px;
    padding-right: 15px;
  }

  .hero-info-title-wrap {
    max-width: 92%;
  }

  .hero-info-title {
    font-size: 44px;
    line-height: 54px;
  }

  .hero-info-d {
    margin-top: 18px;
  }

  .hero-bottom-wrapper {
    margin-top: 30px;
  }

  .partners-section {
    padding-bottom: 60px;
  }

  .partners-log {
    width: 124px;
  }

  .showcase-section {
    padding-bottom: 60px;
  }

  .showcase-item {
    height: 240px;
  }

  .about-section {
    padding-bottom: 50px;
  }

  .about-top-content {
    grid-column-gap: 30px;
    grid-row-gap: 30px;
  }

  .about-right-descption {
    font-size: 22px;
  }

  .service-section {
    padding-top: 50px;
    padding-bottom: 50px;
  }

  .title-wrap {
    max-width: 90%;
    margin-top: 14px;
  }

  .title-descption-wrap {
    margin-top: 14px;
  }

  .service-itme-content {
    grid-column-gap: 17px;
    grid-row-gap: 17px;
    margin-top: 50px;
  }

  .service-itme-top-left {
    grid-column-gap: 12px;
    grid-row-gap: 12px;
  }

  .service-itme-top-right {
    grid-column-gap: 20px;
    grid-row-gap: 20px;
    max-width: 70%;
  }

  .service-list-itme {
    margin-top: 24px;
  }

  .work-process-seciton {
    padding-top: 60px;
  }

  .work-content {
    margin-top: 50px;
  }

  .work-grid {
    grid-column-gap: 20px;
    grid-row-gap: 20px;
    grid-template-columns: 1fr;
  }

  .pricing-content {
    grid-template-columns: 1fr;
  }

  .testimonial-section {
    padding-top: 60px;
    padding-bottom: 60px;
  }

  .testimonial-content {
    grid-column-gap: 22px;
    grid-row-gap: 22px;
    margin-top: 50px;
  }

  .testimonial {
    grid-column-gap: 22px;
    grid-row-gap: 22px;
    grid-template-columns: 1fr;
  }

  .faq-section {
    padding-top: 60px;
    padding-bottom: 60px;
  }

  .faq-content {
    margin-top: 40px;
  }

  .faq-toggle {
    grid-column-gap: 30px;
    grid-row-gap: 30px;
  }

  .footer-section {
    padding-bottom: 15px;
  }

  .footer-content-wrapper {
    padding-left: 30px;
    padding-right: 30px;
  }

  .footer-background {
    border-radius: 16px;
    padding-top: 40px;
    padding-bottom: 40px;
  }

  .footer-top-content {
    grid-column-gap: 37px;
    grid-row-gap: 37px;
  }

  .footer-title {
    font-size: 72px;
    line-height: 80px;
  }

  .footer-s-link {
    font-size: 28px;
    line-height: 36px;
  }

  .footer-bottom-content {
    grid-column-gap: 20px;
    grid-row-gap: 20px;
    flex-flow: wrap;
    margin-top: 70px;
  }

  .title-p {
    letter-spacing: -1px;
    font-size: 40px;
    line-height: 54px;
  }

  .contact-pop-up {
    border-radius: 16px;
    padding: 30px;
  }

  .contact-pop-up-close-icon-wrap {
    top: 30px;
    right: 30px;
  }

  .contact-pop-up-close-icon {
    width: 21px;
  }

  .contact-pop-up-title {
    max-width: 100%;
  }

  .partners-log-wrapper, .p-wrap {
    grid-column-gap: 60px;
    grid-row-gap: 60px;
  }

  .info-hero-descption {
    max-width: 60%;
    margin-top: 18px;
  }

  .style-guide-section {
    margin-bottom: 70px;
    padding-top: 70px;
    padding-bottom: 80px;
  }

  .style-guide-content-wrapper {
    grid-column-gap: 50px;
    grid-row-gap: 50px;
  }

  .templates-content-block {
    border-radius: 20px;
    padding: 20px;
  }

  .style-title-wrap {
    padding: 20px;
  }

  .style-title {
    font-size: 30px;
  }

  .colors-wrapper {
    grid-template-columns: 1fr 1fr;
    margin-top: 50px;
  }

  .typography-item-block {
    padding: 28px 20px;
  }

  .info-hero-title {
    margin-top: 22px;
    font-size: 50px;
    line-height: 63px;
  }

  .licenses-section {
    margin-bottom: 70px;
    padding-top: 70px;
    padding-bottom: 70px;
  }

  .license-content-gird {
    grid-template-columns: 1fr;
  }

  .changelog-section {
    padding-top: 80px;
    padding-bottom: 80px;
  }

  .changelog-title {
    font-size: 44px;
    line-height: 51px;
  }

  .footer-bottolink-wrap {
    grid-column-gap: 20px;
    grid-row-gap: 20px;
    flex-flow: wrap;
    grid-template-rows: auto auto;
    grid-template-columns: 1fr 1fr;
    grid-auto-columns: 1fr;
    display: flex;
  }

  .work-experience-section {
    padding-top: 70px;
    padding-bottom: 70px;
  }

  .work-experience-top-title {
    width: 100%;
    max-width: 90%;
  }

  .work-experiencec-top-descption {
    margin-top: 20px;
  }

  .work-experience-bottom-contetn {
    margin-top: 40px;
  }

  .work-e-left, .work-e-right {
    width: auto;
  }

  .work-e-title.n {
    display: none;
  }

  .service-right-title-2 {
    font-size: 24px;
    line-height: 29px;
  }

  .work-process-content {
    grid-column-gap: 16px;
    grid-row-gap: 16px;
  }
}

@media screen and (max-width: 479px) {
  .nav-wrapper {
    justify-content: space-between;
    padding-top: 15px;
    padding-left: 15px;
    padding-right: 15px;
  }

  .navbar {
    width: 100%;
    padding: 12px;
  }

  .nav-menu-wrapper {
    display: none;
  }

  .nav-log {
    width: 34px;
  }

  .nav-button {
    padding-top: 8px;
    padding-bottom: 8px;
  }

  .hero-section {
    padding-top: 60px;
    padding-bottom: 60px;
  }

  .hero-top-image {
    width: 10px;
    height: 10px;
  }

  .hero-top-info-title {
    font-size: 17px;
  }

  .hero-info-title-wrap {
    max-width: 100%;
    margin-top: 14px;
  }

  .hero-info-title {
    letter-spacing: -1.6px;
    font-size: 30px;
    line-height: 42px;
  }

  .hero-info-d {
    font-size: 15px;
  }

  .hero-bottom-wrapper {
    grid-column-gap: 11px;
    grid-row-gap: 11px;
    flex-flow: wrap;
  }

  .button {
    font-size: 15px;
  }

  .partners-section {
    padding-bottom: 40px;
  }

  .partners-log {
    width: 100px;
  }

  .showcase-section {
    padding-bottom: 50px;
  }

  .showcase-item {
    height: 140px;
  }

  .about-section {
    padding-bottom: 40px;
  }

  .about-top-content {
    grid-column-gap: 27px;
    grid-row-gap: 27px;
  }

  .about-right-descption {
    font-size: 20px;
  }

  .service-section {
    padding-top: 50px;
    padding-bottom: 50px;
  }

  .title-wrap {
    max-width: 100%;
    margin-top: 12px;
  }

  .title-descption-wrap {
    margin-top: 12px;
  }

  .service-itme-content {
    grid-column-gap: 12px;
    grid-row-gap: 12px;
    margin-top: 40px;
  }

  .service-itme {
    border-radius: 12px;
    padding: 16px;
  }

  .service-itme-top-right {
    justify-content: flex-start;
    align-items: center;
    max-width: none;
    margin-left: 12px;
  }

  .service-left-icon, .service-right-button {
    display: none;
  }

  .service-list-itme {
    margin-top: 16px;
  }

  .service-imge {
    border-radius: 8px;
  }

  .servuce-list-descption {
    margin-top: 24px;
    font-size: 16px;
  }

  .work-process-seciton, .work-section {
    padding-top: 50px;
    padding-bottom: 50px;
  }

  .work-content {
    margin-top: 40px;
  }

  .work-card {
    border-radius: 12px;
    padding-bottom: 20px;
    padding-left: 7px;
    padding-right: 7px;
  }

  .work-imge {
    border-radius: 10px;
  }

  .work-info-wrap {
    padding-left: 0;
    padding-right: 0;
  }

  .work-name {
    letter-spacing: 0;
    font-size: 18px;
  }

  .work-date {
    font-size: 18px;
  }

  .pricing-section {
    padding-top: 50px;
    padding-bottom: 50px;
  }

  .pricing-card._2 {
    border-radius: 10px;
    padding: 22px 18px;
  }

  .pricing-button, .pricing-card-bottom {
    margin-top: 30px;
  }

  .price-feature-list-text {
    font-size: 16px;
    line-height: 20px;
  }

  .testimonial-section {
    padding-top: 50px;
    padding-bottom: 50px;
  }

  .testimonial-content {
    grid-column-gap: 20px;
    grid-row-gap: 20px;
    margin-top: 40px;
  }

  .testimonial._1 {
    grid-column-gap: 20px;
    grid-row-gap: 20px;
  }

  .testimonial-card {
    padding: 22px 20px;
  }

  .faq-section {
    padding-top: 50px;
    padding-bottom: 50px;
  }

  .faq-content {
    flex-flow: column;
  }

  .faq-item-wrapper {
    flex-flow: column;
    align-items: stretch;
  }

  .faq-item {
    grid-row-gap: 16px;
    border-radius: 12px;
    flex-flow: column;
    justify-content: center;
    align-items: stretch;
    display: flex;
  }

  .faq-toggle {
    grid-column-gap: 20px;
    grid-row-gap: 20px;
    flex-flow: row;
    justify-content: space-between;
    align-items: center;
    padding: 18px;
    position: relative;
  }

  .faq-toggle.w--open {
    grid-column-gap: 0px;
    grid-row-gap: 0px;
    flex-flow: row;
    align-items: center;
  }

  .faq-title-wrap {
    flex-flow: column;
  }

  .faq-title {
    width: 91%;
    font-size: 16px;
    line-height: 25px;
  }

  .faq-list.w--open {
    padding-top: 0;
    padding-left: 18px;
    padding-right: 18px;
  }

  .footer-section {
    padding-bottom: 10px;
  }

  .footer-content-wrapper {
    padding-left: 20px;
    padding-right: 20px;
  }

  .footer-background {
    border-radius: 12px;
    padding-top: 30px;
    padding-bottom: 30px;
  }

  .footer-top-content {
    grid-column-gap: 40px;
    grid-row-gap: 40px;
  }

  .footer-title {
    font-size: 41px;
  }

  .footer-s-link {
    font-size: 22px;
    line-height: 32px;
  }

  .footer-s-link-wrap {
    margin-top: 30px;
  }

  .footer-bottom-content {
    grid-column-gap: 18px;
    grid-row-gap: 18px;
    flex-flow: column;
    justify-content: center;
    margin-top: 60px;
  }

  .title-p {
    font-size: 28px;
    line-height: 42px;
  }

  .contact-pop-up {
    border-radius: 14px;
    max-width: 94%;
    height: 84%;
    padding: 16px;
  }

  .contact-pop-up-close-icon-wrap {
    top: 16px;
    right: 16px;
  }

  .contact-pop-up-close-icon {
    width: 18px;
  }

  .contact-pop-up-title {
    font-size: 22px;
    line-height: 30px;
  }

  .partners-log-wrapper, .p-wrap {
    grid-column-gap: 30px;
    grid-row-gap: 30px;
  }

  .templates-info-hero {
    padding-top: 80px;
  }

  .info-hero-descption {
    max-width: 100%;
  }

  .style-guide-section {
    margin-bottom: 60px;
  }

  .templates-content-block {
    padding: 16px;
  }

  .style-title-wrap._1 {
    border-radius: 12px;
    padding: 18px;
  }

  .style-title {
    font-size: 28px;
    line-height: 38px;
  }

  .colors-wrapper {
    grid-template-columns: 1fr;
  }

  .typography-item-block {
    padding-left: 0;
    padding-right: 0;
  }

  .text-block {
    line-height: 23px;
  }

  .button-block {
    flex-flow: wrap;
  }

  .info-hero-title {
    margin-top: 20px;
    font-size: 47px;
    line-height: 60px;
  }

  .licenses-section {
    margin-bottom: 60px;
  }

  .changelog-section {
    padding-top: 70px;
    padding-bottom: 70px;
  }

  .changelog-title {
    font-size: 37px;
    line-height: 44px;
  }

  .footer-bottolink-wrap {
    grid-column-gap: 20px;
    grid-row-gap: 20px;
    flex-flow: wrap;
    justify-content: center;
    align-items: center;
  }

  .work-experience-section {
    padding-top: 60px;
    padding-bottom: 60px;
  }

  .work-experience-top-title {
    font-size: 23px;
    line-height: 36px;
  }

  .work-experiencec-top-descption {
    font-size: 15px;
  }

  .work-experience-bottom-contetn {
    grid-column-gap: 27px;
    grid-row-gap: 27px;
  }

  .work-e-left {
    grid-column-gap: 11px;
    grid-row-gap: 11px;
    flex-flow: column;
    display: flex;
  }

  .work-e-title {
    display: none;
  }

  .work-e-title.n {
    display: block;
  }

  .service-right-title-2 {
    font-size: 19px;
    line-height: 25px;
  }

  .service-right-button-2 {
    display: none;
  }

  .service-right-button-2.n {
    justify-content: center;
    align-items: center;
    margin-top: 24px;
    display: flex;
  }

  .work-process-content {
    grid-column-gap: 24px;
    grid-row-gap: 24px;
    grid-template-columns: 1fr;
  }
}

#w-node-_362bdda7-dfb0-8b47-726c-209f1d39926b-ae018dbf {
  grid-area: span 1 / span 1 / span 1 / span 1;
}